# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: kaggle_evaluation.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17kaggle_evaluation.proto\x12\x18kaggle_evaluation_client\"\xf9\x01\n\x17KaggleEvaluationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12/\n\x04\x61rgs\x18\x02 \x03(\x0b\x32!.kaggle_evaluation_client.Payload\x12M\n\x06kwargs\x18\x03 \x03(\x0b\x32=.kaggle_evaluation_client.KaggleEvaluationRequest.KwargsEntry\x1aP\n\x0bKwargsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x30\n\x05value\x18\x02 \x01(\x0b\x32!.kaggle_evaluation_client.Payload:\x02\x38\x01\"N\n\x18KaggleEvaluationResponse\x12\x32\n\x07payload\x18\x01 \x01(\x0b\x32!.kaggle_evaluation_client.Payload\"\x8d\x04\n\x07Payload\x12\x13\n\tstr_value\x18\x01 \x01(\tH\x00\x12\x14\n\nbool_value\x18\x02 \x01(\x08H\x00\x12\x13\n\tint_value\x18\x03 \x01(\x12H\x00\x12\x15\n\x0b\x66loat_value\x18\x04 \x01(\x02H\x00\x12\x14\n\nnone_value\x18\x05 \x01(\x08H\x00\x12;\n\nlist_value\x18\x06 \x01(\x0b\x32%.kaggle_evaluation_client.PayloadListH\x00\x12<\n\x0btuple_value\x18\x07 \x01(\x0b\x32%.kaggle_evaluation_client.PayloadListH\x00\x12:\n\ndict_value\x18\x08 \x01(\x0b\x32$.kaggle_evaluation_client.PayloadMapH\x00\x12 \n\x16pandas_dataframe_value\x18\t \x01(\x0cH\x00\x12 \n\x16polars_dataframe_value\x18\n \x01(\x0cH\x00\x12\x1d\n\x13pandas_series_value\x18\x0b \x01(\x0cH\x00\x12\x1d\n\x13polars_series_value\x18\x0c \x01(\x0cH\x00\x12\x1b\n\x11numpy_array_value\x18\r \x01(\x0cH\x00\x12\x1c\n\x12numpy_scalar_value\x18\x0e \x01(\x0cH\x00\x12\x18\n\x0e\x62ytes_io_value\x18\x0f \x01(\x0cH\x00\x42\x07\n\x05value\"B\n\x0bPayloadList\x12\x33\n\x08payloads\x18\x01 \x03(\x0b\x32!.kaggle_evaluation_client.Payload\"\xad\x01\n\nPayloadMap\x12I\n\x0bpayload_map\x18\x01 \x03(\x0b\x32\x34.kaggle_evaluation_client.PayloadMap.PayloadMapEntry\x1aT\n\x0fPayloadMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x30\n\x05value\x18\x02 \x01(\x0b\x32!.kaggle_evaluation_client.Payload:\x02\x38\x01\x32\x8a\x01\n\x17KaggleEvaluationService\x12o\n\x04Send\x12\x31.kaggle_evaluation_client.KaggleEvaluationRequest\x1a\x32.kaggle_evaluation_client.KaggleEvaluationResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'kaggle_evaluation_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_KAGGLEEVALUATIONREQUEST_KWARGSENTRY']._options = None
  _globals['_KAGGLEEVALUATIONREQUEST_KWARGSENTRY']._serialized_options = b'8\001'
  _globals['_PAYLOADMAP_PAYLOADMAPENTRY']._options = None
  _globals['_PAYLOADMAP_PAYLOADMAPENTRY']._serialized_options = b'8\001'
  _globals['_KAGGLEEVALUATIONREQUEST']._serialized_start=54
  _globals['_KAGGLEEVALUATIONREQUEST']._serialized_end=303
  _globals['_KAGGLEEVALUATIONREQUEST_KWARGSENTRY']._serialized_start=223
  _globals['_KAGGLEEVALUATIONREQUEST_KWARGSENTRY']._serialized_end=303
  _globals['_KAGGLEEVALUATIONRESPONSE']._serialized_start=305
  _globals['_KAGGLEEVALUATIONRESPONSE']._serialized_end=383
  _globals['_PAYLOAD']._serialized_start=386
  _globals['_PAYLOAD']._serialized_end=911
  _globals['_PAYLOADLIST']._serialized_start=913
  _globals['_PAYLOADLIST']._serialized_end=979
  _globals['_PAYLOADMAP']._serialized_start=982
  _globals['_PAYLOADMAP']._serialized_end=1155
  _globals['_PAYLOADMAP_PAYLOADMAPENTRY']._serialized_start=1071
  _globals['_PAYLOADMAP_PAYLOADMAPENTRY']._serialized_end=1155
  _globals['_KAGGLEEVALUATIONSERVICE']._serialized_start=1158
  _globals['_KAGGLEEVALUATIONSERVICE']._serialized_end=1296
# @@protoc_insertion_point(module_scope)
