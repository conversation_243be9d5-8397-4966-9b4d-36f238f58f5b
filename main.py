"""
CMI Detect Behavior with Sensor Data - Champion Solution
完整的冠军级解决方案，包含数据预处理、特征工程、多模型集成等
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import StratifiedGroupKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import f1_score
from tqdm.auto import tqdm
import warnings
warnings.filterwarnings('ignore')

# ==================== 配置 ====================
class Config:
    SEED = 42
    N_FOLDS = 5
    BATCH_SIZE = 64
    LEARNING_RATE = 1e-3
    EPOCHS = 100
    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 传感器列
    IMU_COLS = ['acc_x', 'acc_y', 'acc_z', 'rot_w', 'rot_x', 'rot_y', 'rot_z']
    THERMOPILE_COLS = [f'thm_{i}' for i in range(1, 6)]
    TOF_COLS = [f'tof_{i}_v{j}' for i in range(1, 6) for j in range(64)]
    ALL_SENSOR_COLS = IMU_COLS + THERMOPILE_COLS + TOF_COLS

# ==================== 数据预处理 ====================
class DataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
    def create_features(self, df):
        """创建高级特征"""
        features = []

        print("🔧 创建序列特征...")
        for seq_id in tqdm(df['sequence_id'].unique(), desc="处理序列"):
            seq_data = df[df['sequence_id'] == seq_id].copy()
            feat = {'sequence_id': seq_id}
            
            # 基础特征
            feat['seq_length'] = len(seq_data)
            feat['subject'] = seq_data['subject'].iloc[0]
            
            # 传感器可用性
            feat['has_thermopile'] = not seq_data[Config.THERMOPILE_COLS].isna().all().all()
            feat['has_tof'] = not seq_data[Config.TOF_COLS].isna().all().all()
            
            # 行为阶段特征 (只在训练集中存在)
            if 'behavior' in seq_data.columns:
                for behavior in ['Transition', 'Pause', 'Gesture']:
                    behavior_data = seq_data[seq_data['behavior'] == behavior]
                    if len(behavior_data) > 0:
                        # IMU统计特征
                        for col in Config.IMU_COLS:
                            if col in behavior_data.columns:
                                values = behavior_data[col].dropna()
                                if len(values) > 0:
                                    feat[f'{behavior}_{col}_mean'] = values.mean()
                                    feat[f'{behavior}_{col}_std'] = values.std()
                                    feat[f'{behavior}_{col}_max'] = values.max()
                                    feat[f'{behavior}_{col}_min'] = values.min()
                                    feat[f'{behavior}_{col}_range'] = values.max() - values.min()

                        # 温度传感器特征
                        if feat['has_thermopile']:
                            for col in Config.THERMOPILE_COLS:
                                if col in behavior_data.columns:
                                    values = behavior_data[col].dropna()
                                    if len(values) > 0:
                                        feat[f'{behavior}_{col}_mean'] = values.mean()
                                        feat[f'{behavior}_{col}_std'] = values.std()
            else:
                # 测试集：使用整个序列的统计特征
                for col in Config.IMU_COLS:
                    if col in seq_data.columns:
                        values = seq_data[col].dropna()
                        if len(values) > 0:
                            # 为每个行为阶段创建相同的特征（使用整个序列）
                            for behavior in ['Transition', 'Pause', 'Gesture']:
                                feat[f'{behavior}_{col}_mean'] = values.mean()
                                feat[f'{behavior}_{col}_std'] = values.std()
                                feat[f'{behavior}_{col}_max'] = values.max()
                                feat[f'{behavior}_{col}_min'] = values.min()
                                feat[f'{behavior}_{col}_range'] = values.max() - values.min()

                # 温度传感器特征
                if feat['has_thermopile']:
                    for col in Config.THERMOPILE_COLS:
                        if col in seq_data.columns:
                            values = seq_data[col].dropna()
                            if len(values) > 0:
                                for behavior in ['Transition', 'Pause', 'Gesture']:
                                    feat[f'{behavior}_{col}_mean'] = values.mean()
                                    feat[f'{behavior}_{col}_std'] = values.std()
            
            # 目标变量
            if 'gesture' in seq_data.columns:
                feat['gesture'] = seq_data['gesture'].iloc[0]
                feat['is_bfrb'] = 1 if seq_data['sequence_type'].iloc[0] == 'Target' else 0
            
            features.append(feat)
        
        return pd.DataFrame(features)
    
    def prepare_sequences(self, df, max_length=500):
        """准备序列数据"""
        sequences = []

        print("📊 准备序列数据...")
        for seq_id in tqdm(df['sequence_id'].unique(), desc="处理序列数据"):
            seq_data = df[df['sequence_id'] == seq_id].copy()
            seq_data = seq_data.sort_values('sequence_counter')
            
            # 提取传感器数据
            sensor_data = seq_data[Config.ALL_SENSOR_COLS].fillna(0).values
            
            # 填充或截断到固定长度
            if len(sensor_data) > max_length:
                sensor_data = sensor_data[:max_length]
            else:
                padding = np.zeros((max_length - len(sensor_data), sensor_data.shape[1]))
                sensor_data = np.vstack([sensor_data, padding])
            
            sequences.append(sensor_data)
        
        return np.array(sequences)

# ==================== 数据集 ====================
class SensorDataset(Dataset):
    def __init__(self, sequences, features, labels=None):
        self.sequences = torch.FloatTensor(sequences)
        self.features = torch.FloatTensor(features.values)
        self.labels = torch.LongTensor(labels) if labels is not None else None
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        if self.labels is not None:
            return self.sequences[idx], self.features[idx], self.labels[idx]
        return self.sequences[idx], self.features[idx]

# ==================== 模型架构 ====================
class MultiModalNet(nn.Module):
    def __init__(self, seq_input_dim, feat_input_dim, num_classes, hidden_dim=256):
        super().__init__()
        
        # 序列处理分支 - CNN + LSTM
        self.conv1d = nn.Sequential(
            nn.Conv1d(seq_input_dim, 64, kernel_size=7, padding=3),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(64, 128, kernel_size=5, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(50)
        )
        
        self.lstm = nn.LSTM(256, hidden_dim//2, batch_first=True, bidirectional=True, dropout=0.3)
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, dropout=0.3)
        
        # 特征处理分支
        self.feat_net = nn.Sequential(
            nn.Linear(feat_input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.BatchNorm1d(hidden_dim//2),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 融合和分类
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim//2, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(hidden_dim, num_classes)
        )
        
    def forward(self, sequences, features):
        # 序列处理
        x = sequences.transpose(1, 2)  # (batch, features, seq_len)
        x = self.conv1d(x)
        x = x.transpose(1, 2)  # (batch, seq_len, features)
        
        lstm_out, _ = self.lstm(x)
        
        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        seq_features = torch.mean(attn_out, dim=1)  # 全局平均池化
        
        # 特征处理
        feat_out = self.feat_net(features)
        
        # 融合
        combined = torch.cat([seq_features, feat_out], dim=1)
        output = self.classifier(combined)
        
        return output

# ==================== 训练函数 ====================
class Trainer:
    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = torch.optim.AdamW(model.parameters(), lr=Config.LEARNING_RATE, weight_decay=1e-4)
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=Config.EPOCHS)
        
    def train_epoch(self, dataloader):
        self.model.train()
        total_loss = 0

        pbar = tqdm(dataloader, desc="训练中", leave=False)
        for sequences, features, labels in pbar:
            sequences, features, labels = sequences.to(self.device), features.to(self.device), labels.to(self.device)
            
            self.optimizer.zero_grad()
            outputs = self.model(sequences, features)
            loss = self.criterion(outputs, labels)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()

            # 更新进度条显示当前损失
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})

        return total_loss / len(dataloader)
    
    def validate(self, dataloader):
        self.model.eval()
        predictions = []
        targets = []

        with torch.no_grad():
            pbar = tqdm(dataloader, desc="验证中", leave=False)
            for sequences, features, labels in pbar:
                sequences, features = sequences.to(self.device), features.to(self.device)
                outputs = self.model(sequences, features)
                preds = torch.argmax(outputs, dim=1).cpu().numpy()
                predictions.extend(preds)
                targets.extend(labels.numpy())
        
        return predictions, targets

# ==================== 主要训练流程 ====================
def main():
    # 设置随机种子
    torch.manual_seed(Config.SEED)
    np.random.seed(Config.SEED)
    
    # 加载数据
    print("Loading data...")
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # 数据预处理
    processor = DataProcessor()
    
    # 创建特征
    print("Creating features...")
    train_features = processor.create_features(train_df)
    test_features = processor.create_features(test_df)
    
    # 准备序列数据
    print("Preparing sequences...")
    train_sequences = processor.prepare_sequences(train_df)
    test_sequences = processor.prepare_sequences(test_df)
    
    # 编码标签
    train_features['gesture_encoded'] = processor.label_encoder.fit_transform(train_features['gesture'])
    num_classes = len(processor.label_encoder.classes_)
    
    # 特征对齐和缩放
    print("🔧 对齐训练集和测试集特征...")

    # 获取训练集的特征列，排除标识列和目标列
    exclude_cols = ['sequence_id', 'gesture', 'gesture_encoded', 'subject']
    train_feature_cols = [col for col in train_features.columns if col not in exclude_cols]

    # 确保所有特征列都是数值类型或布尔类型
    print(f"🔍 检查特征列数据类型...")
    numeric_cols = []
    for col in train_feature_cols:
        if train_features[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
            numeric_cols.append(col)
        else:
            print(f"⚠️  跳过非数值列: {col} (类型: {train_features[col].dtype})")

    train_feature_cols = numeric_cols

    # 确保测试集有相同的特征列
    for col in train_feature_cols:
        if col not in test_features.columns:
            if col == 'is_bfrb':
                # 测试集中没有is_bfrb列，跳过这个特征
                print(f"⚠️  测试集中跳过目标相关特征: {col}")
                continue
            test_features[col] = 0  # 用0填充缺失的特征
            print(f"⚠️  测试集缺少特征 {col}，用0填充")

    # 移除测试集中不存在的特征
    final_feature_cols = [col for col in train_feature_cols if col in test_features.columns]

    print(f"✅ 最终使用 {len(final_feature_cols)} 个特征列")

    # 特征缩放
    feature_cols = final_feature_cols
    train_feat_scaled = processor.scaler.fit_transform(train_features[feature_cols].fillna(0).astype(float))
    test_feat_scaled = processor.scaler.transform(test_features[feature_cols].fillna(0).astype(float))

    print(f"✅ 特征对齐完成，共 {len(feature_cols)} 个特征")
    
    # 交叉验证
    print("🔄 开始交叉验证...")
    skf = StratifiedGroupKFold(n_splits=Config.N_FOLDS, shuffle=True, random_state=Config.SEED)

    oof_predictions = np.zeros(len(train_features))
    test_predictions = np.zeros((len(test_features), num_classes))

    fold_scores = []

    for fold, (train_idx, val_idx) in enumerate(skf.split(train_features, train_features['gesture_encoded'], train_features['subject'])):
        print(f"\n📂 Fold {fold + 1}/{Config.N_FOLDS}")
        print("=" * 50)
        
        # 准备数据
        train_dataset = SensorDataset(
            train_sequences[train_idx], 
            pd.DataFrame(train_feat_scaled[train_idx]), 
            train_features['gesture_encoded'].iloc[train_idx].values
        )
        val_dataset = SensorDataset(
            train_sequences[val_idx], 
            pd.DataFrame(train_feat_scaled[val_idx]), 
            train_features['gesture_encoded'].iloc[val_idx].values
        )
        
        train_loader = DataLoader(train_dataset, batch_size=Config.BATCH_SIZE, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=Config.BATCH_SIZE, shuffle=False)
        
        # 初始化模型
        model = MultiModalNet(
            seq_input_dim=len(Config.ALL_SENSOR_COLS),
            feat_input_dim=len(feature_cols),
            num_classes=num_classes
        )
        trainer = Trainer(model, Config.DEVICE)
        
        # 训练
        print(f"🚀 开始训练模型...")
        best_score = 0
        patience = 0

        # 创建epoch进度条
        epoch_pbar = tqdm(range(Config.EPOCHS), desc=f"Fold {fold+1} 训练进度")

        for epoch in epoch_pbar:
            train_loss = trainer.train_epoch(train_loader)
            val_preds, val_targets = trainer.validate(val_loader)

            # 计算F1分数
            val_score = f1_score(val_targets, val_preds, average='macro')

            # 更新进度条显示
            epoch_pbar.set_postfix({
                'Train Loss': f'{train_loss:.4f}',
                'Val F1': f'{val_score:.4f}',
                'Best F1': f'{best_score:.4f}',
                'Patience': f'{patience}/15'
            })

            if val_score > best_score:
                best_score = val_score
                patience = 0
                # 保存最佳模型
                torch.save(model.state_dict(), f'best_model_fold_{fold}.pth')
                epoch_pbar.set_description(f"Fold {fold+1} 训练进度 ⭐ 新最佳!")
            else:
                patience += 1
                if patience >= 15:
                    epoch_pbar.set_description(f"Fold {fold+1} 训练进度 ⏹️ 早停")
                    break

            trainer.scheduler.step()
        
        # 加载最佳模型进行预测
        print(f"📊 加载最佳模型进行预测...")
        model.load_state_dict(torch.load(f'best_model_fold_{fold}.pth'))
        val_preds, _ = trainer.validate(val_loader)
        oof_predictions[val_idx] = val_preds
        fold_scores.append(best_score)

        # 测试集预测
        print(f"🔮 生成测试集预测...")
        test_dataset = SensorDataset(test_sequences, pd.DataFrame(test_feat_scaled))
        test_loader = DataLoader(test_dataset, batch_size=Config.BATCH_SIZE, shuffle=False)

        model.eval()
        fold_test_preds = []
        with torch.no_grad():
            test_pbar = tqdm(test_loader, desc="测试集预测", leave=False)
            for sequences, features in test_pbar:
                sequences, features = sequences.to(Config.DEVICE), features.to(Config.DEVICE)
                outputs = model(sequences, features)
                probs = F.softmax(outputs, dim=1).cpu().numpy()
                fold_test_preds.append(probs)

        test_predictions += np.vstack(fold_test_preds) / Config.N_FOLDS

        print(f"✅ Fold {fold+1} 完成! 最佳F1分数: {best_score:.4f}")
        print("-" * 50)
    
    # 计算OOF分数
    oof_score = f1_score(train_features['gesture_encoded'], oof_predictions, average='macro')
    mean_cv_score = np.mean(fold_scores)
    std_cv_score = np.std(fold_scores)

    print("\n" + "="*60)
    print("🎯 交叉验证结果汇总")
    print("="*60)
    print(f"📊 各折分数: {[f'{score:.4f}' for score in fold_scores]}")
    print(f"📈 平均CV分数: {mean_cv_score:.4f} ± {std_cv_score:.4f}")
    print(f"🎯 OOF F1分数: {oof_score:.4f}")
    print("="*60)

    # 生成提交文件
    print("📝 生成提交文件...")
    test_pred_labels = np.argmax(test_predictions, axis=1)
    test_gestures = processor.label_encoder.inverse_transform(test_pred_labels)

    submission = pd.DataFrame({
        'sequence_id': test_features['sequence_id'],
        'gesture': test_gestures
    })
    submission.to_csv('submission.csv', index=False)

    print("✅ 提交文件已保存: submission.csv")
    print(f"📊 预测分布:")
    print(submission['gesture'].value_counts())

# ==================== 高级集成解决方案 ====================
class AdvancedEnsemble:
    def __init__(self):
        self.models = {}
        self.weights = {'multimodal': 0.4, 'transformer': 0.3, 'resnet1d': 0.3}

    def create_transformer_model(self, seq_input_dim, feat_input_dim, num_classes):
        """创建Transformer模型"""
        class TransformerModel(nn.Module):
            def __init__(self, input_dim, feat_dim, num_classes, d_model=256):
                super().__init__()
                self.input_projection = nn.Linear(input_dim, d_model)
                self.pos_encoding = nn.Parameter(torch.randn(500, d_model))

                encoder_layer = nn.TransformerEncoderLayer(
                    d_model=d_model, nhead=8, dim_feedforward=512, dropout=0.1
                )
                self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=6)

                self.feat_net = nn.Sequential(
                    nn.Linear(feat_dim, d_model//2),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )

                self.classifier = nn.Sequential(
                    nn.Linear(d_model + d_model//2, d_model),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(d_model, num_classes)
                )

            def forward(self, sequences, features):
                x = self.input_projection(sequences)
                x = x + self.pos_encoding[:x.size(1)]
                x = x.transpose(0, 1)  # (seq_len, batch, d_model)

                transformer_out = self.transformer(x)
                seq_features = torch.mean(transformer_out, dim=0)

                feat_out = self.feat_net(features)
                combined = torch.cat([seq_features, feat_out], dim=1)

                return self.classifier(combined)

        return TransformerModel(seq_input_dim, feat_input_dim, num_classes)

    def create_resnet1d_model(self, seq_input_dim, feat_input_dim, num_classes):
        """创建ResNet1D模型"""
        class ResNet1DBlock(nn.Module):
            def __init__(self, in_channels, out_channels, kernel_size=3):
                super().__init__()
                self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2)
                self.bn1 = nn.BatchNorm1d(out_channels)
                self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, padding=kernel_size//2)
                self.bn2 = nn.BatchNorm1d(out_channels)

                self.shortcut = nn.Sequential()
                if in_channels != out_channels:
                    self.shortcut = nn.Sequential(
                        nn.Conv1d(in_channels, out_channels, 1),
                        nn.BatchNorm1d(out_channels)
                    )

            def forward(self, x):
                residual = self.shortcut(x)
                out = F.relu(self.bn1(self.conv1(x)))
                out = self.bn2(self.conv2(out))
                out += residual
                return F.relu(out)

        class ResNet1DModel(nn.Module):
            def __init__(self, input_dim, feat_dim, num_classes):
                super().__init__()
                self.conv1 = nn.Conv1d(input_dim, 64, 7, padding=3)
                self.bn1 = nn.BatchNorm1d(64)
                self.maxpool = nn.MaxPool1d(3, stride=2, padding=1)

                self.layer1 = self._make_layer(64, 64, 2)
                self.layer2 = self._make_layer(64, 128, 2)
                self.layer3 = self._make_layer(128, 256, 2)
                self.layer4 = self._make_layer(256, 512, 2)

                self.avgpool = nn.AdaptiveAvgPool1d(1)

                self.feat_net = nn.Sequential(
                    nn.Linear(feat_dim, 256),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )

                self.classifier = nn.Sequential(
                    nn.Linear(512 + 256, 512),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(512, num_classes)
                )

            def _make_layer(self, in_channels, out_channels, blocks):
                layers = []
                layers.append(ResNet1DBlock(in_channels, out_channels))
                for _ in range(1, blocks):
                    layers.append(ResNet1DBlock(out_channels, out_channels))
                return nn.Sequential(*layers)

            def forward(self, sequences, features):
                x = sequences.transpose(1, 2)
                x = F.relu(self.bn1(self.conv1(x)))
                x = self.maxpool(x)

                x = self.layer1(x)
                x = self.layer2(x)
                x = self.layer3(x)
                x = self.layer4(x)

                x = self.avgpool(x)
                seq_features = x.view(x.size(0), -1)

                feat_out = self.feat_net(features)
                combined = torch.cat([seq_features, feat_out], dim=1)

                return self.classifier(combined)

        return ResNet1DModel(seq_input_dim, feat_input_dim, num_classes)

def safe_read_csv(filepath, **kwargs):
    """安全读取CSV文件，处理各种编码和解析问题"""
    try:
        # 首先尝试默认设置
        return pd.read_csv(filepath, **kwargs)
    except UnicodeDecodeError:
        print(f"⚠️  编码问题，尝试使用latin-1编码读取 {filepath}")
        return pd.read_csv(filepath, encoding='latin-1', **kwargs)
    except pd.errors.ParserError as e:
        print(f"⚠️  解析错误，尝试使用Python引擎读取 {filepath}")
        print(f"错误详情: {e}")
        return pd.read_csv(filepath, engine='python', **kwargs)
    except FileNotFoundError:
        print(f"❌ 文件未找到: {filepath}")
        print("请确保数据文件在正确的路径下")
        raise
    except Exception as e:
        print(f"❌ 读取文件时发生未知错误: {filepath}")
        print(f"错误详情: {e}")
        raise

def advanced_main():
    """高级集成训练主函数"""
    torch.manual_seed(Config.SEED)
    np.random.seed(Config.SEED)

    print("📂 加载数据文件...")

    # 尝试不同的文件路径
    possible_paths = [
        # 本地路径
        ('train.csv', 'test.csv'),
        # Kaggle路径
        ('/kaggle/input/cmi-detect-behavior-with-sensor-data/train.csv',
         '/kaggle/input/cmi-detect-behavior-with-sensor-data/test.csv'),
        # 其他可能的路径
        ('data/train.csv', 'data/test.csv'),
    ]

    train_df = None
    test_df = None

    for train_path, test_path in possible_paths:
        try:
            print(f"🔍 尝试路径: {train_path}")
            train_df = safe_read_csv(train_path)
            test_df = safe_read_csv(test_path)
            print(f"✅ 成功加载数据!")
            print(f"📊 训练集形状: {train_df.shape}")
            print(f"📊 测试集形状: {test_df.shape}")
            break
        except Exception as e:
            print(f"❌ 路径 {train_path} 失败: {str(e)}")
            continue

    if train_df is None or test_df is None:
        print("❌ 无法加载数据文件，请检查文件路径和格式")
        print("💡 提示：请确保以下文件存在：")
        print("   - train.csv")
        print("   - test.csv")
        print("   - train_demographics.csv (可选)")
        print("   - test_demographics.csv (可选)")
        return

    processor = DataProcessor()
    ensemble = AdvancedEnsemble()

    print("Creating features...")
    train_features = processor.create_features(train_df)
    test_features = processor.create_features(test_df)

    print("Preparing sequences...")
    train_sequences = processor.prepare_sequences(train_df)
    test_sequences = processor.prepare_sequences(test_df)

    train_features['gesture_encoded'] = processor.label_encoder.fit_transform(train_features['gesture'])
    num_classes = len(processor.label_encoder.classes_)

    # 获取特征列，排除标识列和目标列
    exclude_cols = ['sequence_id', 'gesture', 'gesture_encoded', 'subject']
    feature_cols = [col for col in train_features.columns if col not in exclude_cols]

    # 确保所有特征列都是数值类型或布尔类型
    print(f"🔍 检查特征列数据类型...")
    numeric_cols = []
    for col in feature_cols:
        if train_features[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
            numeric_cols.append(col)
        else:
            print(f"⚠️  跳过非数值列: {col} (类型: {train_features[col].dtype})")

    feature_cols = numeric_cols

    # 确保测试集有相同的特征列
    for col in feature_cols:
        if col not in test_features.columns:
            if col == 'is_bfrb':
                # 测试集中没有is_bfrb列，跳过这个特征
                print(f"⚠️  测试集中跳过目标相关特征: {col}")
                continue
            test_features[col] = 0  # 用0填充缺失的特征
            print(f"⚠️  测试集缺少特征 {col}，用0填充")

    # 移除测试集中不存在的特征
    final_feature_cols = [col for col in feature_cols if col in test_features.columns]
    feature_cols = final_feature_cols

    print(f"✅ 最终使用 {len(feature_cols)} 个特征列")

    # 填充缺失值并进行特征缩放
    train_feat_scaled = processor.scaler.fit_transform(train_features[feature_cols].fillna(0).astype(float))
    test_feat_scaled = processor.scaler.transform(test_features[feature_cols].fillna(0).astype(float))

    # 多模型集成训练
    model_predictions = {}

    print("\n" + "="*60)
    print("🤖 开始多模型集成训练")
    print("="*60)

    for model_idx, model_name in enumerate(['multimodal', 'transformer', 'resnet1d']):
        print(f"\n🚀 训练模型 {model_idx+1}/3: {model_name.upper()}")
        print("-" * 40)

        skf = StratifiedGroupKFold(n_splits=Config.N_FOLDS, shuffle=True, random_state=Config.SEED)
        oof_preds = np.zeros(len(train_features))
        test_preds = np.zeros((len(test_features), num_classes))

        fold_pbar = tqdm(
            enumerate(skf.split(train_features, train_features['gesture_encoded'], train_features['subject'])),
            total=Config.N_FOLDS,
            desc=f"{model_name} 折验证"
        )

        for fold, (train_idx, val_idx) in fold_pbar:
            fold_pbar.set_description(f"{model_name} Fold {fold + 1}/{Config.N_FOLDS}")

            # 选择模型架构
            if model_name == 'multimodal':
                model = MultiModalNet(len(Config.ALL_SENSOR_COLS), len(feature_cols), num_classes)
            elif model_name == 'transformer':
                model = ensemble.create_transformer_model(len(Config.ALL_SENSOR_COLS), len(feature_cols), num_classes)
            else:  # resnet1d
                model = ensemble.create_resnet1d_model(len(Config.ALL_SENSOR_COLS), len(feature_cols), num_classes)

            trainer = Trainer(model, Config.DEVICE)

            # 准备数据
            train_dataset = SensorDataset(
                train_sequences[train_idx],
                pd.DataFrame(train_feat_scaled[train_idx]),
                train_features['gesture_encoded'].iloc[train_idx].values
            )
            val_dataset = SensorDataset(
                train_sequences[val_idx],
                pd.DataFrame(train_feat_scaled[val_idx]),
                train_features['gesture_encoded'].iloc[val_idx].values
            )

            train_loader = DataLoader(train_dataset, batch_size=Config.BATCH_SIZE, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=Config.BATCH_SIZE, shuffle=False)

            # 训练模型
            best_score = 0
            patience = 0

            # 创建训练进度条
            epoch_pbar = tqdm(range(Config.EPOCHS), desc=f"{model_name} F{fold+1} 训练", leave=False)

            for epoch in epoch_pbar:
                train_loss = trainer.train_epoch(train_loader)
                val_preds, val_targets = trainer.validate(val_loader)
                val_score = f1_score(val_targets, val_preds, average='macro')

                # 更新进度条
                epoch_pbar.set_postfix({
                    'Loss': f'{train_loss:.4f}',
                    'F1': f'{val_score:.4f}',
                    'Best': f'{best_score:.4f}',
                    'Pat': f'{patience}/15'
                })

                if val_score > best_score:
                    best_score = val_score
                    patience = 0
                    torch.save(model.state_dict(), f'{model_name}_fold_{fold}.pth')
                    epoch_pbar.set_description(f"{model_name} F{fold+1} 训练 ⭐")
                else:
                    patience += 1
                    if patience >= 15:
                        epoch_pbar.set_description(f"{model_name} F{fold+1} 训练 ⏹️")
                        break

                trainer.scheduler.step()

            # 验证集预测
            model.load_state_dict(torch.load(f'{model_name}_fold_{fold}.pth'))
            val_preds, _ = trainer.validate(val_loader)
            oof_preds[val_idx] = val_preds

            # 测试集预测
            test_dataset = SensorDataset(test_sequences, pd.DataFrame(test_feat_scaled))
            test_loader = DataLoader(test_dataset, batch_size=Config.BATCH_SIZE, shuffle=False)

            model.eval()
            fold_test_preds = []
            with torch.no_grad():
                test_pbar = tqdm(test_loader, desc=f"{model_name} F{fold+1} 测试", leave=False)
                for sequences, features in test_pbar:
                    sequences, features = sequences.to(Config.DEVICE), features.to(Config.DEVICE)
                    outputs = model(sequences, features)
                    probs = F.softmax(outputs, dim=1).cpu().numpy()
                    fold_test_preds.append(probs)

            test_preds += np.vstack(fold_test_preds) / Config.N_FOLDS

            # 更新折验证进度条
            fold_pbar.set_postfix({'Best_F1': f'{best_score:.4f}'})

        model_predictions[model_name] = {
            'oof': oof_preds,
            'test': test_preds
        }

        oof_score = f1_score(train_features['gesture_encoded'], oof_preds, average='macro')
        print(f"✅ {model_name.upper()} 完成! OOF F1: {oof_score:.4f}")
        print("-" * 40)

    # 集成预测
    print("\n" + "="*60)
    print("🎯 集成预测阶段")
    print("="*60)

    print("🔄 正在集成多个模型的预测...")
    final_test_preds = np.zeros((len(test_features), num_classes))

    print("📊 集成权重:")
    for model_name, weight in ensemble.weights.items():
        if model_name in model_predictions:
            final_test_preds += weight * model_predictions[model_name]['test']
            print(f"   {model_name}: {weight:.2f}")

    # 生成最终提交
    print("📝 生成最终提交文件...")
    test_pred_labels = np.argmax(final_test_preds, axis=1)
    test_gestures = processor.label_encoder.inverse_transform(test_pred_labels)

    submission = pd.DataFrame({
        'sequence_id': test_features['sequence_id'],
        'gesture': test_gestures
    })
    submission.to_csv('advanced_submission.csv', index=False)

    print("✅ 高级集成提交文件已保存: advanced_submission.csv")
    print(f"📊 最终预测分布:")
    print(submission['gesture'].value_counts())
    print("="*60)
    print("🎉 训练完成! 祝你在比赛中取得好成绩!")

if __name__ == "__main__":
    # 运行高级集成解决方案
    advanced_main()
