"""
测试修复后的代码
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# 配置
class Config:
    SEED = 42
    # 传感器列
    IMU_COLS = ['acc_x', 'acc_y', 'acc_z', 'rot_w', 'rot_x', 'rot_y', 'rot_z']
    THERMOPILE_COLS = [f'thm_{i}' for i in range(1, 6)]
    TOF_COLS = [f'tof_{i}_v{j}' for i in range(1, 6) for j in range(64)]
    ALL_SENSOR_COLS = IMU_COLS + THERMOPILE_COLS + TOF_COLS

# 数据预处理类
class DataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
    def create_features(self, df):
        """创建高级特征"""
        features = []

        print("🔧 创建序列特征...")
        for seq_id in df['sequence_id'].unique()[:5]:  # 只处理前5个序列进行测试
            seq_data = df[df['sequence_id'] == seq_id].copy()
            feat = {'sequence_id': seq_id}
            
            # 基础特征
            feat['seq_length'] = len(seq_data)
            feat['subject'] = seq_data['subject'].iloc[0]
            
            # 传感器可用性
            feat['has_thermopile'] = not seq_data[Config.THERMOPILE_COLS].isna().all().all()
            feat['has_tof'] = not seq_data[Config.TOF_COLS].isna().all().all()
            
            # 目标变量
            if 'gesture' in seq_data.columns:
                feat['gesture'] = seq_data['gesture'].iloc[0]
                feat['is_bfrb'] = 1 if seq_data['sequence_type'].iloc[0] == 'Target' else 0
            
            features.append(feat)
        
        return pd.DataFrame(features)

def test_data_loading():
    """测试数据加载和特征创建"""
    print("📂 加载数据文件...")
    
    # 尝试不同的文件路径
    possible_paths = [
        ('kaggle/input/cmi-detect-behavior-with-sensor-data/train.csv',
         'kaggle/input/cmi-detect-behavior-with-sensor-data/test.csv'),
    ]
    
    train_df = None
    test_df = None
    
    for train_path, test_path in possible_paths:
        try:
            print(f"🔍 尝试路径: {train_path}")
            train_df = pd.read_csv(train_path)
            test_df = pd.read_csv(test_path)
            print(f"✅ 成功加载数据!")
            print(f"📊 训练集形状: {train_df.shape}")
            print(f"📊 测试集形状: {test_df.shape}")
            break
        except Exception as e:
            print(f"❌ 路径 {train_path} 失败: {str(e)}")
            continue
    
    if train_df is None or test_df is None:
        print("❌ 无法加载数据文件")
        return
    
    # 检查数据结构
    print("\n=== 数据结构检查 ===")
    print(f"训练集序列数: {train_df['sequence_id'].nunique()}")
    print(f"测试集序列数: {test_df['sequence_id'].nunique()}")
    print(f"手势类型: {train_df['gesture'].unique()}")
    print(f"序列类型: {train_df['sequence_type'].unique()}")
    
    # 测试特征创建
    print("\n=== 测试特征创建 ===")
    processor = DataProcessor()
    
    train_features = processor.create_features(train_df)
    test_features = processor.create_features(test_df)
    
    print(f"训练特征形状: {train_features.shape}")
    print(f"测试特征形状: {test_features.shape}")
    print(f"训练特征列: {list(train_features.columns)}")
    print(f"测试特征列: {list(test_features.columns)}")
    
    # 检查特征对齐
    print("\n=== 特征对齐测试 ===")
    exclude_cols = ['sequence_id', 'gesture', 'gesture_encoded', 'subject']
    train_feature_cols = [col for col in train_features.columns if col not in exclude_cols]
    
    print(f"训练集特征列: {train_feature_cols}")
    
    # 检查数据类型
    print("\n=== 数据类型检查 ===")
    for col in train_feature_cols:
        dtype = train_features[col].dtype
        print(f"{col}: {dtype}")
        if dtype not in ['int64', 'float64', 'int32', 'float32', 'bool']:
            print(f"  ⚠️ 非数值类型!")
    
    # 检查测试集中缺少的列
    print("\n=== 测试集缺失列检查 ===")
    for col in train_feature_cols:
        if col not in test_features.columns:
            print(f"测试集缺少: {col}")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    test_data_loading()
