import pandas as pd
import numpy as np

# 读取数据
train_df = pd.read_csv('kaggle/input/cmi-detect-behavior-with-sensor-data/train.csv')
test_df = pd.read_csv('kaggle/input/cmi-detect-behavior-with-sensor-data/test.csv')

print('=== 训练集基本信息 ===')
print(f'训练集形状: {train_df.shape}')
print(f'列数: {len(train_df.columns)}')
print(f'序列数: {train_df["sequence_id"].nunique()}')
print(f'受试者数: {train_df["subject"].nunique()}')

print('\n=== 手势分布 ===')
print(train_df['gesture'].value_counts())

print('\n=== 序列类型分布 ===')
print(train_df['sequence_type'].value_counts())

print('\n=== 行为阶段分布 ===')
print(train_df['behavior'].value_counts())

print('\n=== 测试集基本信息 ===')
print(f'测试集形状: {test_df.shape}')
print(f'测试集序列数: {test_df["sequence_id"].nunique()}')

print('\n=== 检查测试集列 ===')
print('测试集缺少的列:')
train_cols = set(train_df.columns)
test_cols = set(test_df.columns)
missing_in_test = train_cols - test_cols
print(missing_in_test)

print('\n=== 检查数据类型 ===')
print('非数值列:')
for col in train_df.columns:
    if train_df[col].dtype not in ['int64', 'float64', 'int32', 'float32']:
        print(f'{col}: {train_df[col].dtype}')

print('\n=== 检查缺失值 ===')
print('训练集缺失值:')
missing_train = train_df.isnull().sum()
print(missing_train[missing_train > 0].head(10))

print('\n=== 示例序列 ===')
sample_seq = train_df[train_df['sequence_id'] == train_df['sequence_id'].iloc[0]]
print(f'示例序列长度: {len(sample_seq)}')
print(f'示例序列行为: {sample_seq["behavior"].unique()}')
print(f'示例序列手势: {sample_seq["gesture"].iloc[0]}')
